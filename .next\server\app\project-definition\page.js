/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/project-definition/page";
exports.ids = ["app/project-definition/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproject-definition%2Fpage&page=%2Fproject-definition%2Fpage&appPaths=%2Fproject-definition%2Fpage&pagePath=private-next-app-dir%2Fproject-definition%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproject-definition%2Fpage&page=%2Fproject-definition%2Fpage&appPaths=%2Fproject-definition%2Fpage&pagePath=private-next-app-dir%2Fproject-definition%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'project-definition',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/project-definition/page.tsx */ \"(rsc)/./src/app/project-definition/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\project-definition\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\project-definition\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/project-definition/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/project-definition/page\",\n        pathname: \"/project-definition\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproject-definition%2Fpage&page=%2Fproject-definition%2Fpage&appPaths=%2Fproject-definition%2Fpage&pagePath=private-next-app-dir%2Fproject-definition%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ThemeProvider.tsx */ \"(ssr)/./src/components/ThemeProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2ZhaXNzJTVDJTVDRGVza3RvcCU1QyU1Q0NvbnRleHRLaXQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNmYWlzcyU1QyU1Q0Rlc2t0b3AlNUMlNUNDb250ZXh0S2l0JTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q1RoZW1lUHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVGhlbWVQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0xBQWlKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29udGV4dGtpdC8/ODQzMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRoZW1lUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxmYWlzc1xcXFxEZXNrdG9wXFxcXENvbnRleHRLaXRcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcVGhlbWVQcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cproject-definition%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cproject-definition%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/project-definition/page.tsx */ \"(ssr)/./src/app/project-definition/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2ZhaXNzJTVDJTVDRGVza3RvcCU1QyU1Q0NvbnRleHRLaXQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwcm9qZWN0LWRlZmluaXRpb24lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc0xBQWtIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29udGV4dGtpdC8/ZjZlYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGZhaXNzXFxcXERlc2t0b3BcXFxcQ29udGV4dEtpdFxcXFxzcmNcXFxcYXBwXFxcXHByb2plY3QtZGVmaW5pdGlvblxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cproject-definition%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/project-definition/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/project-definition/page.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectDefinition)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ModuleLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ModuleLayout */ \"(ssr)/./src/components/ModuleLayout.tsx\");\n/* harmony import */ var _components_SmartQuestion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/SmartQuestion */ \"(ssr)/./src/components/SmartQuestion.tsx\");\n/* harmony import */ var _components_OutputPanel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/OutputPanel */ \"(ssr)/./src/components/OutputPanel.tsx\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ProjectDefinition() {\n    const { projectDefinition, updateProjectDefinition } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_4__.useContextStore)();\n    const questions = [\n        {\n            id: \"name\",\n            question: \"What is the name of your AI project?\",\n            questionAr: \"ما هو اسم مشروع الذكاء الاصطناعي الخاص بك؟\",\n            placeholder: \"e.g., Smart Customer Support Bot, Content Generator AI, etc.\",\n            placeholderAr: \"مثال: بوت دعم العملاء الذكي، مولد المحتوى بالذكاء الاصطناعي، إلخ.\",\n            type: \"text\",\n            aiSuggestion: \"Choose a clear, descriptive name that reflects your project's main function and target audience.\",\n            aiSuggestionAr: \"اختر اسماً واضحاً ووصفياً يعكس الوظيفة الرئيسية لمشروعك والجمهور المستهدف.\",\n            promptTemplate: 'Help me refine this AI project name: \"{answer}\". Suggest improvements for clarity and market appeal.'\n        },\n        {\n            id: \"purpose\",\n            question: \"What is the main purpose or problem your AI project aims to solve?\",\n            questionAr: \"ما هو الهدف الرئيسي أو المشكلة التي يهدف مشروع الذكاء الاصطناعي لحلها؟\",\n            placeholder: \"Describe the core problem you want to address...\",\n            placeholderAr: \"صف المشكلة الأساسية التي تريد معالجتها...\",\n            aiSuggestion: \"Focus on a specific, measurable problem. Avoid being too broad or vague.\",\n            aiSuggestionAr: \"ركز على مشكلة محددة وقابلة للقياس. تجنب أن تكون عاماً أو غامضاً.\",\n            promptTemplate: 'Analyze this problem statement for an AI project: \"{answer}\". Help me make it more specific and actionable.'\n        },\n        {\n            id: \"targetUsers\",\n            question: \"Who are the primary users or beneficiaries of this project?\",\n            questionAr: \"من هم المستخدمون الأساسيون أو المستفيدون من هذا المشروع؟\",\n            placeholder: \"e.g., Customer service teams, Content creators, Students, etc.\",\n            placeholderAr: \"مثال: فرق خدمة العملاء، منشئو المحتوى، الطلاب، إلخ.\",\n            aiSuggestion: \"Be specific about user demographics, roles, and their current pain points.\",\n            aiSuggestionAr: \"كن محدداً حول التركيبة السكانية للمستخدمين وأدوارهم ونقاط الألم الحالية لديهم.\",\n            promptTemplate: 'Help me create detailed user personas for this target audience: \"{answer}\". Include their needs and challenges.'\n        }\n    ];\n    const handleFieldChange = (field, value)=>{\n        updateProjectDefinition({\n            [field]: value\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModuleLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        title: \"Project Definition\",\n        titleAr: \"تعريف المشروع\",\n        subtitle: \"Define the scope, users, and goals of your AI project\",\n        subtitleAr: \"حدد نطاق مشروعك والمستخدمين والأهداف\",\n        emoji: \"\\uD83C\\uDFAF\",\n        moduleKey: \"project-definition\",\n        backLink: {\n            href: \"/\",\n            label: \"← Back to Home\",\n            labelAr: \"← العودة للرئيسية\"\n        },\n        nextLink: {\n            href: \"/context-map\",\n            label: \"Next: Context Map →\",\n            labelAr: \"التالي: خريطة السياق ←\"\n        },\n        rightPanel: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OutputPanel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            moduleData: projectDefinition,\n            moduleName: \"Project Definition\",\n            moduleNameAr: \"تعريف المشروع\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\project-definition\\\\page.tsx\",\n            lineNumber: 68,\n            columnNumber: 9\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: questions.map((question)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SmartQuestion__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    id: question.id,\n                    question: question.question,\n                    questionAr: question.questionAr,\n                    placeholder: question.placeholder,\n                    placeholderAr: question.placeholderAr,\n                    value: projectDefinition[question.id] || \"\",\n                    onChange: (value)=>handleFieldChange(question.id, value),\n                    type: question.type,\n                    aiSuggestion: question.aiSuggestion,\n                    aiSuggestionAr: question.aiSuggestionAr,\n                    promptTemplate: question.promptTemplate\n                }, question.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\project-definition\\\\page.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\project-definition\\\\page.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\project-definition\\\\page.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/project-definition/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AutoSaveIndicator.tsx":
/*!**********************************************!*\
  !*** ./src/components/AutoSaveIndicator.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AutoSaveIndicator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction AutoSaveIndicator() {\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    // Simulate auto-save functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const interval = setInterval(()=>{\n            setSaving(true);\n            setTimeout(()=>{\n                setLastSaved(new Date());\n                setSaving(false);\n            }, 500);\n        }, 10000); // Auto-save every 10 seconds\n        return ()=>clearInterval(interval);\n    }, []);\n    if (!lastSaved && !saving) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg px-4 py-2 flex items-center space-x-2\",\n            children: saving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AutoSaveIndicator.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                        children: isArabic ? \"جاري الحفظ...\" : \"Saving...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AutoSaveIndicator.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-2 h-2 bg-green-500 rounded-full\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AutoSaveIndicator.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                        children: [\n                            isArabic ? \"تم الحفظ\" : \"Saved\",\n                            \" \",\n                            lastSaved?.toLocaleTimeString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AutoSaveIndicator.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AutoSaveIndicator.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AutoSaveIndicator.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AutoSaveIndicator.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _ThemeToggle__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ThemeToggle */ \"(ssr)/./src/components/ThemeToggle.tsx\");\n/* harmony import */ var _LanguageToggle__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./LanguageToggle */ \"(ssr)/./src/components/LanguageToggle.tsx\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction Header({ title, subtitle, backLink, emoji }) {\n    const { currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_4__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `fixed top-4 ${isArabic ? \"left-4\" : \"right-4\"} z-50 flex gap-3`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white/90 dark:bg-gray-800/90 backdrop-blur-md rounded-2xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 p-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/\",\n                                className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 hover:from-gray-100 hover:to-gray-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n                                title: isArabic ? \"الصفحة الرئيسية\" : \"Home\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-6 h-6 text-gray-600 dark:text-gray-300 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/settings\",\n                                className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-purple-50 to-violet-100 dark:from-gray-800 dark:to-gray-900 hover:from-purple-100 hover:to-violet-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n                                title: isArabic ? \"إعدادات API\" : \"API Settings\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-6 h-6 text-purple-600 dark:text-purple-400 transition-all duration-300 group-hover:rotate-90\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LanguageToggle__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThemeToggle__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-8 pt-4\",\n                children: [\n                    backLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        href: backLink.href,\n                        className: \"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mb-4 inline-block transition-colors\",\n                        children: backLink.label\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-2\",\n                        children: [\n                            emoji && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-2\",\n                                children: emoji\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 21\n                            }, this),\n                            title\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\",\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LanguageToggle.tsx":
/*!*******************************************!*\
  !*** ./src/components/LanguageToggle.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LanguageToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction LanguageToggle() {\n    const { currentLanguage, setLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: ()=>setLanguage(currentLanguage === \"ar\" ? \"en\" : \"ar\"),\n        className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-green-50 to-emerald-100 dark:from-gray-800 dark:to-gray-900 hover:from-green-100 hover:to-emerald-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n        \"aria-label\": currentLanguage === \"ar\" ? \"Switch to English\" : \"التبديل إلى العربية\",\n        title: currentLanguage === \"ar\" ? \"Switch to English\" : \"التبديل إلى العربية\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-sm font-bold text-blue-600 dark:text-blue-400 transition-all duration-500 ease-in-out ${currentLanguage === \"en\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 rotate-180 scale-0\"}`,\n                        children: \"EN\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-sm font-bold text-green-600 dark:text-green-400 absolute transition-all duration-500 ease-in-out font-arabic ${currentLanguage === \"ar\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 -rotate-180 scale-0\"}`,\n                        children: \"عر\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LanguageToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ModuleLayout.tsx":
/*!*****************************************!*\
  !*** ./src/components/ModuleLayout.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModuleLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Header */ \"(ssr)/./src/components/Header.tsx\");\n/* harmony import */ var _ProgressIndicator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ProgressIndicator */ \"(ssr)/./src/components/ProgressIndicator.tsx\");\n/* harmony import */ var _AutoSaveIndicator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AutoSaveIndicator */ \"(ssr)/./src/components/AutoSaveIndicator.tsx\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ModuleLayout({ title, titleAr, subtitle, subtitleAr, emoji, moduleKey, backLink, nextLink, children, rightPanel }) {\n    const { currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_4__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    title: isArabic ? titleAr : title,\n                    subtitle: isArabic ? subtitleAr : subtitle,\n                    emoji: emoji,\n                    backLink: backLink ? {\n                        href: backLink.href,\n                        label: isArabic ? backLink.labelAr : backLink.label\n                    } : undefined\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProgressIndicator__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    currentModule: moduleKey\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-8 max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `space-y-6 ${isArabic ? \"lg:order-2\" : \"lg:order-1\"}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl mr-3\",\n                                                children: \"✍️\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                                                children: isArabic ? \"الأسئلة الذكية\" : \"Smart Questions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 15\n                                    }, this),\n                                    children\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `space-y-6 ${isArabic ? \"lg:order-1\" : \"lg:order-2\"}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 sticky top-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl mr-3\",\n                                                    children: \"\\uD83D\\uDCC4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                                                    children: isArabic ? \"المخرجات المجمّعة\" : \"Generated Outputs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this),\n                                    rightPanel\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this),\n                (backLink || nextLink) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mt-12 max-w-7xl mx-auto\",\n                    children: [\n                        backLink ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: backLink.href,\n                            className: \"flex items-center px-6 py-3 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"mr-2\",\n                                    children: \"←\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 17\n                                }, this),\n                                isArabic ? backLink.labelAr : backLink.label\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 15\n                        }, this),\n                        nextLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: nextLink.href,\n                            className: \"flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\",\n                            children: [\n                                isArabic ? nextLink.labelAr : nextLink.label,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2\",\n                                    children: \"→\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AutoSaveIndicator__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ModuleLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/OutputPanel.tsx":
/*!****************************************!*\
  !*** ./src/components/OutputPanel.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OutputPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction OutputPanel({ moduleData, moduleName, moduleNameAr }) {\n    const { currentLanguage, outputFormat, setOutputFormat } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isArabic = currentLanguage === \"ar\";\n    // تحويل البيانات إلى تنسيقات مختلفة\n    const generateMarkdown = ()=>{\n        const title = isArabic ? moduleNameAr : moduleName;\n        let markdown = `# ${title}\\n\\n`;\n        Object.entries(moduleData).forEach(([key, value])=>{\n            if (value && typeof value === \"string\" && value.trim()) {\n                const formattedKey = key.replace(/([A-Z])/g, \" $1\").replace(/^./, (str)=>str.toUpperCase());\n                markdown += `## ${formattedKey}\\n${value}\\n\\n`;\n            }\n        });\n        return markdown;\n    };\n    const generateHTML = ()=>{\n        const title = isArabic ? moduleNameAr : moduleName;\n        let html = `<div class=\"module-output\">\\n  <h1>${title}</h1>\\n`;\n        Object.entries(moduleData).forEach(([key, value])=>{\n            if (value && typeof value === \"string\" && value.trim()) {\n                const formattedKey = key.replace(/([A-Z])/g, \" $1\").replace(/^./, (str)=>str.toUpperCase());\n                html += `  <section>\\n    <h2>${formattedKey}</h2>\\n    <p>${value}</p>\\n  </section>\\n`;\n            }\n        });\n        html += \"</div>\";\n        return html;\n    };\n    const generateJSON = ()=>{\n        const filteredData = Object.fromEntries(Object.entries(moduleData).filter(([_, value])=>value && typeof value === \"string\" && value.trim()));\n        return JSON.stringify({\n            module: isArabic ? moduleNameAr : moduleName,\n            data: filteredData,\n            metadata: {\n                timestamp: new Date().toISOString(),\n                language: isArabic ? \"ar\" : \"en\",\n                version: \"1.0\"\n            }\n        }, null, 2);\n    };\n    const generateYAML = ()=>{\n        const filteredData = Object.fromEntries(Object.entries(moduleData).filter(([_, value])=>value && typeof value === \"string\" && value.trim()));\n        let yaml = `# ${isArabic ? moduleNameAr : moduleName}\\n`;\n        yaml += `# Generated: ${new Date().toISOString()}\\n\\n`;\n        Object.entries(filteredData).forEach(([key, value])=>{\n            const formattedKey = key.replace(/([A-Z])/g, \"_$1\").toLowerCase();\n            yaml += `${formattedKey}: |\\n`;\n            const lines = String(value || \"\").split(\"\\n\");\n            lines.forEach((line)=>{\n                yaml += `  ${line}\\n`;\n            });\n            yaml += \"\\n\";\n        });\n        return yaml;\n    };\n    const getCurrentOutput = ()=>{\n        switch(outputFormat){\n            case \"markdown\":\n                return generateMarkdown();\n            case \"html\":\n                return generateHTML();\n            case \"json\":\n                return generateJSON();\n            case \"yaml\":\n                return generateYAML();\n            default:\n                return generateMarkdown();\n        }\n    };\n    const handleCopyAll = async ()=>{\n        const output = getCurrentOutput();\n        await navigator.clipboard.writeText(output);\n        setCopied(true);\n        setTimeout(()=>setCopied(false), 2000);\n    };\n    const handleDownload = ()=>{\n        const output = getCurrentOutput();\n        const extensions = {\n            markdown: \"md\",\n            html: \"html\",\n            json: \"json\",\n            yaml: \"yml\"\n        };\n        const extension = extensions[outputFormat] || \"txt\";\n        const filename = `${moduleName.toLowerCase().replace(/\\s+/g, \"-\")}.${extension}`;\n        const mimeTypes = {\n            markdown: \"text/markdown\",\n            html: \"text/html\",\n            json: \"application/json\",\n            yaml: \"text/yaml\"\n        };\n        const mimeType = mimeTypes[outputFormat] || \"text/plain\";\n        const blob = new Blob([\n            output\n        ], {\n            type: mimeType\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = filename;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    };\n    const hasData = Object.values(moduleData).some((value)=>value && typeof value === \"string\" && value.trim());\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2 flex-wrap gap-2\",\n                        children: [\n                            \"markdown\",\n                            \"html\",\n                            \"json\",\n                            \"yaml\"\n                        ].map((format)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setOutputFormat(format),\n                                className: `px-3 py-1 text-sm rounded-lg transition-colors ${outputFormat === format ? \"bg-blue-600 text-white\" : \"bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600\"}`,\n                                children: format.toUpperCase()\n                            }, format, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    hasData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCopyAll,\n                                className: \"flex items-center px-3 py-1 text-sm bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/30 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-1\",\n                                        children: \"\\uD83D\\uDCCB\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this),\n                                    copied ? isArabic ? \"تم النسخ!\" : \"Copied!\" : isArabic ? \"نسخ الكل\" : \"Copy All\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleDownload,\n                                className: \"flex items-center px-3 py-1 text-sm bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-900/30 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-1\",\n                                        children: \"\\uD83D\\uDCBE\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this),\n                                    isArabic ? \"تحميل\" : \"Download\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 dark:bg-gray-900 rounded-lg p-4 min-h-[300px]\",\n                children: hasData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                    className: `text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap font-mono overflow-x-auto ${isArabic ? \"text-right\" : \"text-left\"}`,\n                    children: getCurrentOutput()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full text-gray-500 dark:text-gray-400\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-4xl mb-2 block\",\n                                children: \"\\uD83D\\uDCDD\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: isArabic ? \"ابدأ بالإجابة على الأسئلة لرؤية المخرجات\" : \"Start answering questions to see outputs\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                children: [\n                    outputFormat === \"markdown\" && (isArabic ? \"تنسيق Markdown - جاهز للاستخدام في المستندات\" : \"Markdown format - Ready for documentation\"),\n                    outputFormat === \"html\" && (isArabic ? \"تنسيق HTML - جاهز للمواقع الإلكترونية\" : \"HTML format - Ready for websites\"),\n                    outputFormat === \"json\" && (isArabic ? \"تنسيق JSON - جاهز للبرمجة والـ APIs\" : \"JSON format - Ready for programming and APIs\"),\n                    outputFormat === \"yaml\" && (isArabic ? \"تنسيق YAML - جاهز للتكوين والنشر\" : \"YAML format - Ready for configuration and deployment\")\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\OutputPanel.tsx\",\n        lineNumber: 140,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/OutputPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ProgressIndicator.tsx":
/*!**********************************************!*\
  !*** ./src/components/ProgressIndicator.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProgressIndicator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ProgressIndicator({ currentModule }) {\n    const { projectDefinition, contextMap, emotionalTone, technicalLayer, legalRisk, currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    const modules = [\n        {\n            key: \"project-definition\",\n            name: \"Project Definition\",\n            nameAr: \"تعريف المشروع\",\n            emoji: \"\\uD83C\\uDFAF\",\n            data: projectDefinition,\n            href: \"/project-definition\"\n        },\n        {\n            key: \"context-map\",\n            name: \"Context Map\",\n            nameAr: \"خريطة السياق\",\n            emoji: \"\\uD83D\\uDDFA️\",\n            data: contextMap,\n            href: \"/context-map\"\n        },\n        {\n            key: \"emotional-tone\",\n            name: \"Emotional Tone\",\n            nameAr: \"النبرة العاطفية\",\n            emoji: \"✨\",\n            data: emotionalTone,\n            href: \"/emotional-tone\"\n        },\n        {\n            key: \"technical-layer\",\n            name: \"Technical Layer\",\n            nameAr: \"الطبقة التقنية\",\n            emoji: \"⚙️\",\n            data: technicalLayer,\n            href: \"/technical-layer\"\n        },\n        {\n            key: \"legal-risk\",\n            name: \"Legal & Privacy\",\n            nameAr: \"القانونية والخصوصية\",\n            emoji: \"\\uD83D\\uDD12\",\n            data: legalRisk,\n            href: \"/legal-risk\"\n        }\n    ];\n    const getModuleProgress = (moduleData)=>{\n        const totalFields = Object.keys(moduleData).length;\n        const filledFields = Object.values(moduleData).filter((value)=>value && typeof value === \"string\" && value.trim()).length;\n        return totalFields > 0 ? filledFields / totalFields * 100 : 0;\n    };\n    const overallProgress = modules.reduce((total, module)=>{\n        return total + getModuleProgress(module.data);\n    }, 0) / modules.length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                        children: isArabic ? \"تقدم المشروع\" : \"Project Progress\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                        children: [\n                            Math.round(overallProgress),\n                            \"% \",\n                            isArabic ? \"مكتمل\" : \"Complete\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                    style: {\n                        width: `${overallProgress}%`\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-5 gap-4\",\n                children: modules.map((module)=>{\n                    const progress = getModuleProgress(module.data);\n                    const isCurrent = currentModule === module.key;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: module.href,\n                        className: `p-3 rounded-lg border-2 transition-all hover:shadow-md ${isCurrent ? \"border-blue-500 bg-blue-50 dark:bg-blue-900/20\" : \"border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl mb-2\",\n                                    children: module.emoji\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs font-medium text-gray-900 dark:text-white mb-2\",\n                                    children: isArabic ? module.nameAr : module.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1 mb-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `h-1 rounded-full transition-all duration-300 ${progress === 100 ? \"bg-green-500\" : \"bg-blue-500\"}`,\n                                        style: {\n                                            width: `${progress}%`\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                    children: [\n                                        Math.round(progress),\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 15\n                        }, this)\n                    }, module.key, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 flex justify-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/final-preview\",\n                        className: \"flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-1\",\n                                children: \"\\uD83D\\uDCCB\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this),\n                            isArabic ? \"المعاينة النهائية\" : \"Final Preview\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this),\n                    overallProgress > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            if (confirm(isArabic ? \"هل أنت متأكد من إعادة تعيين جميع البيانات؟\" : \"Are you sure you want to reset all data?\")) {\n                                // Reset functionality would go here\n                                window.location.reload();\n                            }\n                        },\n                        className: \"flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg text-sm transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-1\",\n                                children: \"\\uD83D\\uDD04\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this),\n                            isArabic ? \"إعادة تعيين\" : \"Reset\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProgressIndicator.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ProgressIndicator.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SmartQuestion.tsx":
/*!******************************************!*\
  !*** ./src/components/SmartQuestion.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SmartQuestion)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction SmartQuestion({ id, question, questionAr, placeholder, placeholderAr, value, onChange, type = \"textarea\", aiSuggestion, aiSuggestionAr, promptTemplate }) {\n    const { currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const [showSuggestion, setShowSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isArabic = currentLanguage === \"ar\";\n    const handleCopy = async ()=>{\n        if (value.trim()) {\n            await navigator.clipboard.writeText(value);\n            setCopied(true);\n            setTimeout(()=>setCopied(false), 2000);\n        }\n    };\n    const handlePromptCopy = async ()=>{\n        if (promptTemplate && value.trim()) {\n            const prompt = promptTemplate.replace(\"{answer}\", value);\n            await navigator.clipboard.writeText(prompt);\n            setCopied(true);\n            setTimeout(()=>setCopied(false), 2000);\n        }\n    };\n    const handleSuggestionApply = ()=>{\n        if (aiSuggestion || aiSuggestionAr) {\n            const suggestion = isArabic ? aiSuggestionAr : aiSuggestion;\n            if (suggestion && !value.trim()) {\n                onChange(suggestion);\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 dark:text-white mb-2\",\n                                children: isArabic ? questionAr : question\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this),\n                            (aiSuggestion || aiSuggestionAr) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowSuggestion(!showSuggestion),\n                                className: \"text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-1\",\n                                        children: \"\\uD83E\\uDDE0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this),\n                                    isArabic ? \"عرض الاقتراح الذكي\" : \"Show AI Suggestion\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    value.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleCopy,\n                        className: \"flex items-center px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-1\",\n                                children: \"\\uD83D\\uDCCE\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 13\n                            }, this),\n                            copied ? isArabic ? \"تم النسخ!\" : \"Copied!\" : isArabic ? \"نسخ\" : \"Copy\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            showSuggestion && (aiSuggestion || aiSuggestionAr) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-blue-500 mr-2\",\n                            children: \"\\uD83D\\uDCA1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-blue-800 dark:text-blue-200\",\n                            children: isArabic ? aiSuggestionAr : aiSuggestion\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, this),\n            type === \"textarea\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                value: value,\n                onChange: (e)=>onChange(e.target.value),\n                placeholder: isArabic ? placeholderAr : placeholder,\n                className: `w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none ${isArabic ? \"text-right\" : \"text-left\"}`,\n                rows: 4,\n                dir: isArabic ? \"rtl\" : \"ltr\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 111,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                type: \"text\",\n                value: value,\n                onChange: (e)=>onChange(e.target.value),\n                placeholder: isArabic ? placeholderAr : placeholder,\n                className: `w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white ${isArabic ? \"text-right\" : \"text-left\"}`,\n                dir: isArabic ? \"rtl\" : \"ltr\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 122,\n                columnNumber: 9\n            }, this),\n            promptTemplate && value.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handlePromptCopy,\n                    className: \"flex items-center px-4 py-2 text-sm bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/30 transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"mr-1\",\n                            children: \"\\uD83D\\uDE80\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 13\n                        }, this),\n                        isArabic ? \"نسخ كـ Prompt\" : \"Copy as Prompt\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SmartQuestion.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children }) {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize theme from localStorage or system preference\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedTheme = localStorage.getItem(\"theme\");\n        const systemTheme = window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n        const initialTheme = savedTheme || systemTheme;\n        setTheme(initialTheme);\n        setMounted(true);\n    }, []);\n    // Apply theme to document\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mounted) {\n            const root = document.documentElement;\n            if (theme === \"dark\") {\n                root.classList.add(\"dark\");\n            } else {\n                root.classList.remove(\"dark\");\n            }\n            localStorage.setItem(\"theme\", theme);\n        }\n    }, [\n        theme,\n        mounted\n    ]);\n    const toggleTheme = ()=>{\n        setTheme((prev)=>prev === \"light\" ? \"dark\" : \"light\");\n    };\n    // Prevent hydration mismatch\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            toggleTheme\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeProvider.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        // Return default values instead of throwing error during SSR\n        return {\n            theme: \"light\",\n            toggleTheme: ()=>{}\n        };\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ThemeProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ThemeToggle.tsx":
/*!****************************************!*\
  !*** ./src/components/ThemeToggle.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ThemeProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ThemeProvider */ \"(ssr)/./src/components/ThemeProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ThemeToggle() {\n    const { theme, toggleTheme } = (0,_ThemeProvider__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: toggleTheme,\n        className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 hover:from-blue-100 hover:to-indigo-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n        \"aria-label\": `Switch to ${theme === \"light\" ? \"dark\" : \"light\"} mode`,\n        title: `Switch to ${theme === \"light\" ? \"dark\" : \"light\"} mode`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: `w-6 h-6 text-amber-500 transition-all duration-500 ease-in-out ${theme === \"light\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 rotate-180 scale-0\"}`,\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: `w-6 h-6 text-blue-400 absolute transition-all duration-500 ease-in-out ${theme === \"dark\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 -rotate-180 scale-0\"}`,\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9UaGVtZVRvZ2dsZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFMkM7QUFFNUIsU0FBU0M7SUFDdEIsTUFBTSxFQUFFQyxLQUFLLEVBQUVDLFdBQVcsRUFBRSxHQUFHSCx3REFBUUE7SUFFdkMscUJBQ0UsOERBQUNJO1FBQ0NDLFNBQVNGO1FBQ1RHLFdBQVU7UUFDVkMsY0FBWSxDQUFDLFVBQVUsRUFBRUwsVUFBVSxVQUFVLFNBQVMsUUFBUSxLQUFLLENBQUM7UUFDcEVNLE9BQU8sQ0FBQyxVQUFVLEVBQUVOLFVBQVUsVUFBVSxTQUFTLFFBQVEsS0FBSyxDQUFDOzswQkFFL0QsOERBQUNPO2dCQUFJSCxXQUFVOztrQ0FFYiw4REFBQ0k7d0JBQ0NKLFdBQVcsQ0FBQywrREFBK0QsRUFDekVKLFVBQVUsVUFDTixtQ0FDQSwrQkFDTCxDQUFDO3dCQUNGUyxNQUFLO3dCQUNMQyxRQUFPO3dCQUNQQyxTQUFRO3dCQUNSQyxPQUFNO2tDQUVOLDRFQUFDQzs0QkFDQ0MsZUFBYzs0QkFDZEMsZ0JBQWU7NEJBQ2ZDLGFBQWE7NEJBQ2JDLEdBQUU7Ozs7Ozs7Ozs7O2tDQUtOLDhEQUFDVDt3QkFDQ0osV0FBVyxDQUFDLHVFQUF1RSxFQUNqRkosVUFBVSxTQUNOLG1DQUNBLGdDQUNMLENBQUM7d0JBQ0ZTLE1BQUs7d0JBQ0xDLFFBQU87d0JBQ1BDLFNBQVE7d0JBQ1JDLE9BQU07a0NBRU4sNEVBQUNDOzRCQUNDQyxlQUFjOzRCQUNkQyxnQkFBZTs0QkFDZkMsYUFBYTs0QkFDYkMsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTVIsOERBQUNWO2dCQUFJSCxXQUFVOzs7Ozs7Ozs7Ozs7QUFHckIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb250ZXh0a2l0Ly4vc3JjL2NvbXBvbmVudHMvVGhlbWVUb2dnbGUudHN4PzU0MGYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VUaGVtZSB9IGZyb20gJy4vVGhlbWVQcm92aWRlcic7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFRoZW1lVG9nZ2xlKCkge1xuICBjb25zdCB7IHRoZW1lLCB0b2dnbGVUaGVtZSB9ID0gdXNlVGhlbWUoKTtcblxuICByZXR1cm4gKFxuICAgIDxidXR0b25cbiAgICAgIG9uQ2xpY2s9e3RvZ2dsZVRoZW1lfVxuICAgICAgY2xhc3NOYW1lPVwicmVsYXRpdmUgdy0xMiBoLTEyIHJvdW5kZWQteGwgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ibHVlLTUwIHRvLWluZGlnby0xMDAgZGFyazpmcm9tLWdyYXktODAwIGRhcms6dG8tZ3JheS05MDAgaG92ZXI6ZnJvbS1ibHVlLTEwMCBob3Zlcjp0by1pbmRpZ28tMjAwIGRhcms6aG92ZXI6ZnJvbS1ncmF5LTcwMCBkYXJrOmhvdmVyOnRvLWdyYXktODAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBlYXNlLWluLW91dCBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsIGJvcmRlciBib3JkZXItZ3JheS0yMDAgZGFyazpib3JkZXItZ3JheS02MDAgZ3JvdXAgb3ZlcmZsb3ctaGlkZGVuXCJcbiAgICAgIGFyaWEtbGFiZWw9e2BTd2l0Y2ggdG8gJHt0aGVtZSA9PT0gJ2xpZ2h0JyA/ICdkYXJrJyA6ICdsaWdodCd9IG1vZGVgfVxuICAgICAgdGl0bGU9e2BTd2l0Y2ggdG8gJHt0aGVtZSA9PT0gJ2xpZ2h0JyA/ICdkYXJrJyA6ICdsaWdodCd9IG1vZGVgfVxuICAgID5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICB7LyogU3VuIGljb24gKi99XG4gICAgICAgIDxzdmdcbiAgICAgICAgICBjbGFzc05hbWU9e2B3LTYgaC02IHRleHQtYW1iZXItNTAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTUwMCBlYXNlLWluLW91dCAke1xuICAgICAgICAgICAgdGhlbWUgPT09ICdsaWdodCdcbiAgICAgICAgICAgICAgPyAnb3BhY2l0eS0xMDAgcm90YXRlLTAgc2NhbGUtMTAwJ1xuICAgICAgICAgICAgICA6ICdvcGFjaXR5LTAgcm90YXRlLTE4MCBzY2FsZS0wJ1xuICAgICAgICAgIH1gfVxuICAgICAgICAgIGZpbGw9XCJub25lXCJcbiAgICAgICAgICBzdHJva2U9XCJjdXJyZW50Q29sb3JcIlxuICAgICAgICAgIHZpZXdCb3g9XCIwIDAgMjQgMjRcIlxuICAgICAgICAgIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIlxuICAgICAgICA+XG4gICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcbiAgICAgICAgICAgIHN0cm9rZVdpZHRoPXsyfVxuICAgICAgICAgICAgZD1cIk0xMiAzdjFtMCAxNnYxbTktOWgtMU00IDEySDNtMTUuMzY0IDYuMzY0bC0uNzA3LS43MDdNNi4zNDMgNi4zNDNsLS43MDctLjcwN20xMi43MjggMGwtLjcwNy43MDdNNi4zNDMgMTcuNjU3bC0uNzA3LjcwN00xNiAxMmE0IDQgMCAxMS04IDAgNCA0IDAgMDE4IDB6XCJcbiAgICAgICAgICAvPlxuICAgICAgICA8L3N2Zz5cblxuICAgICAgICB7LyogTW9vbiBpY29uICovfVxuICAgICAgICA8c3ZnXG4gICAgICAgICAgY2xhc3NOYW1lPXtgdy02IGgtNiB0ZXh0LWJsdWUtNDAwIGFic29sdXRlIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTUwMCBlYXNlLWluLW91dCAke1xuICAgICAgICAgICAgdGhlbWUgPT09ICdkYXJrJ1xuICAgICAgICAgICAgICA/ICdvcGFjaXR5LTEwMCByb3RhdGUtMCBzY2FsZS0xMDAnXG4gICAgICAgICAgICAgIDogJ29wYWNpdHktMCAtcm90YXRlLTE4MCBzY2FsZS0wJ1xuICAgICAgICAgIH1gfVxuICAgICAgICAgIGZpbGw9XCJub25lXCJcbiAgICAgICAgICBzdHJva2U9XCJjdXJyZW50Q29sb3JcIlxuICAgICAgICAgIHZpZXdCb3g9XCIwIDAgMjQgMjRcIlxuICAgICAgICAgIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIlxuICAgICAgICA+XG4gICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcbiAgICAgICAgICAgIHN0cm9rZVdpZHRoPXsyfVxuICAgICAgICAgICAgZD1cIk0yMC4zNTQgMTUuMzU0QTkgOSAwIDAxOC42NDYgMy42NDYgOS4wMDMgOS4wMDMgMCAwMDEyIDIxYTkuMDAzIDkuMDAzIDAgMDA4LjM1NC01LjY0NnpcIlxuICAgICAgICAgIC8+XG4gICAgICAgIDwvc3ZnPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBTdWJ0bGUgZ2xvdyBlZmZlY3QgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tciBmcm9tLXRyYW5zcGFyZW50IHZpYS13aGl0ZS8xMCB0by10cmFuc3BhcmVudCBvcGFjaXR5LTAgZ3JvdXAtaG92ZXI6b3BhY2l0eS0xMDAgdHJhbnNpdGlvbi1vcGFjaXR5IGR1cmF0aW9uLTMwMFwiIC8+XG4gICAgPC9idXR0b24+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlVGhlbWUiLCJUaGVtZVRvZ2dsZSIsInRoZW1lIiwidG9nZ2xlVGhlbWUiLCJidXR0b24iLCJvbkNsaWNrIiwiY2xhc3NOYW1lIiwiYXJpYS1sYWJlbCIsInRpdGxlIiwiZGl2Iiwic3ZnIiwiZmlsbCIsInN0cm9rZSIsInZpZXdCb3giLCJ4bWxucyIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ThemeToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/store/contextStore.ts":
/*!***********************************!*\
  !*** ./src/store/contextStore.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useContextStore: () => (/* binding */ useContextStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\n// القيم الافتراضية\nconst initialState = {\n    projectDefinition: {\n        name: \"\",\n        purpose: \"\",\n        targetUsers: \"\",\n        goals: \"\",\n        scope: \"\",\n        timeline: \"\"\n    },\n    contextMap: {\n        timeContext: \"\",\n        language: \"\",\n        location: \"\",\n        culturalContext: \"\",\n        behavioralAspects: \"\",\n        environmentalFactors: \"\"\n    },\n    emotionalTone: {\n        personality: \"\",\n        communicationStyle: \"\",\n        userExperience: \"\",\n        brandVoice: \"\",\n        emotionalIntelligence: \"\",\n        interactionFlow: \"\"\n    },\n    technicalLayer: {\n        programmingLanguages: \"\",\n        frameworks: \"\",\n        llmModels: \"\",\n        databases: \"\",\n        apis: \"\",\n        infrastructure: \"\"\n    },\n    legalRisk: {\n        privacyConcerns: \"\",\n        dataProtection: \"\",\n        compliance: \"\",\n        risks: \"\",\n        mitigation: \"\",\n        ethicalConsiderations: \"\"\n    },\n    currentLanguage: \"ar\",\n    outputFormat: \"markdown\",\n    apiSettings: {\n        openaiApiKey: \"\",\n        openrouterApiKey: \"\",\n        customModels: []\n    }\n};\n// إنشاء المتجر مع التخزين المستمر\nconst useContextStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        ...initialState,\n        updateProjectDefinition: (data)=>set((state)=>({\n                    projectDefinition: {\n                        ...state.projectDefinition,\n                        ...data\n                    }\n                })),\n        updateContextMap: (data)=>set((state)=>({\n                    contextMap: {\n                        ...state.contextMap,\n                        ...data\n                    }\n                })),\n        updateEmotionalTone: (data)=>set((state)=>({\n                    emotionalTone: {\n                        ...state.emotionalTone,\n                        ...data\n                    }\n                })),\n        updateTechnicalLayer: (data)=>set((state)=>({\n                    technicalLayer: {\n                        ...state.technicalLayer,\n                        ...data\n                    }\n                })),\n        updateLegalRisk: (data)=>set((state)=>({\n                    legalRisk: {\n                        ...state.legalRisk,\n                        ...data\n                    }\n                })),\n        setLanguage: (lang)=>set({\n                currentLanguage: lang\n            }),\n        setOutputFormat: (format)=>set({\n                outputFormat: format\n            }),\n        setApiSettings: (settings)=>set({\n                apiSettings: settings\n            }),\n        resetAll: ()=>set(initialState),\n        getModuleData: (module)=>{\n            const state = get();\n            switch(module){\n                case \"project\":\n                    return state.projectDefinition;\n                case \"context\":\n                    return state.contextMap;\n                case \"emotional\":\n                    return state.emotionalTone;\n                case \"technical\":\n                    return state.technicalLayer;\n                case \"legal\":\n                    return state.legalRisk;\n                default:\n                    return {};\n            }\n        },\n        getAllData: ()=>{\n            const state = get();\n            return {\n                projectDefinition: state.projectDefinition,\n                contextMap: state.contextMap,\n                emotionalTone: state.emotionalTone,\n                technicalLayer: state.technicalLayer,\n                legalRisk: state.legalRisk\n            };\n        }\n    }), {\n    name: \"contextkit-storage\",\n    version: 1\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/contextStore.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e93baefb0a7b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29udGV4dGtpdC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/N2UzNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImU5M2JhZWZiMGE3YlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_ThemeProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ThemeProvider */ \"(rsc)/./src/components/ThemeProvider.tsx\");\n\n\n\nconst metadata = {\n    title: \"ContextKit - AI Context Builder\",\n    description: \"Create organized, actionable context for AI-driven projects\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased font-arabic\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeProvider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUN1QjtBQUNvQztBQUVwRCxNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLOzswQkFDVCw4REFBQ0M7O2tDQUNDLDhEQUFDQzt3QkFBS0MsS0FBSTt3QkFBYUMsTUFBSzs7Ozs7O2tDQUM1Qiw4REFBQ0Y7d0JBQUtDLEtBQUk7d0JBQWFDLE1BQUs7d0JBQTRCQyxhQUFZOzs7Ozs7Ozs7Ozs7MEJBRXRFLDhEQUFDQztnQkFBS0MsV0FBVTswQkFDZCw0RUFBQ2Qsb0VBQWFBOzhCQUNYSzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLWCIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbnRleHRraXQvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIjtcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgfSBmcm9tIFwiQC9jb21wb25lbnRzL1RoZW1lUHJvdmlkZXJcIjtcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6IFwiQ29udGV4dEtpdCAtIEFJIENvbnRleHQgQnVpbGRlclwiLFxuICBkZXNjcmlwdGlvbjogXCJDcmVhdGUgb3JnYW5pemVkLCBhY3Rpb25hYmxlIGNvbnRleHQgZm9yIEFJLWRyaXZlbiBwcm9qZWN0c1wiLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufTogUmVhZG9ubHk8e1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufT4pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxoZWFkPlxuICAgICAgICA8bGluayByZWw9XCJwcmVjb25uZWN0XCIgaHJlZj1cImh0dHBzOi8vZm9udHMuZ29vZ2xlYXBpcy5jb21cIiAvPlxuICAgICAgICA8bGluayByZWw9XCJwcmVjb25uZWN0XCIgaHJlZj1cImh0dHBzOi8vZm9udHMuZ3N0YXRpYy5jb21cIiBjcm9zc09yaWdpbj1cImFub255bW91c1wiIC8+XG4gICAgICA8L2hlYWQ+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9XCJhbnRpYWxpYXNlZCBmb250LWFyYWJpY1wiPlxuICAgICAgICA8VGhlbWVQcm92aWRlcj5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvVGhlbWVQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiVGhlbWVQcm92aWRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiaGVhZCIsImxpbmsiLCJyZWwiLCJocmVmIiwiY3Jvc3NPcmlnaW4iLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/project-definition/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/project-definition/page.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\project-definition\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\project-definition\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0),
/* harmony export */   useTheme: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx#ThemeProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx#useTheme`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zustand","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproject-definition%2Fpage&page=%2Fproject-definition%2Fpage&appPaths=%2Fproject-definition%2Fpage&pagePath=private-next-app-dir%2Fproject-definition%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();